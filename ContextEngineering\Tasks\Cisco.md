
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**


**Panneau de contrôle arrière-plan pour simuler les modes de transition entre la nuit profonde jusqu'au crépuscule**
Nous allons régler correctement les phases de transition au clic événement sur chaque bouton `NuitProfonde`, `<PERSON><PERSON>`, `LeverDeSoleil`, `<PERSON><PERSON>`, `<PERSON><PERSON>`, `<PERSON><PERSON><PERSON>`, `AprèsMidi`, `CoucherDeSoleil`, `Crépuscule`. 
Components\UI\TimeSimulator.tsx


**Ci-desso<PERSON>, le fichier qui contrôle la dérive du soleil au clic événement sur chaque bouton du contrôle arrière-plan**
Components\Background\SunriseAnimation.tsx


**ci-dessous les répertoires où sont stockés les sons, effects pour les boutons au clic événement**
public\sounds
public\sounds\apres-midi
public\sounds\aube
public\sounds\coucher-soleil
public\sounds\crepuscule
public\sounds\lever-soleil
public\sounds\matin
public\sounds\midi
public\sounds\nuit-profonde

**Ici dessous, les répertoires avec les fichiers respectifs qui contrôlent l'audio.**
Components\Audio
Components\Audio\AmbientSoundManager.tsx
Components\Audio\AudioControlPanel.tsx



**Instructions supplémentaires de la part de Cisco pendant la réalisation des tâches**

Je rédigerai des notes supplémentaires pendant vos tâches. Donc surveillez bien ce fichier 


Bon procédons autrement pour la course du soleil nous allons reprendre à zéro parce que ça ne va pas du tout Il faudrait déjà commencer par la fonction `Aube`, le bouton `Aube` sur le panneau Contrôle Arrière-plan Ce qu'il faudrait faire c'est imaginons que la moitié de l'écran, traçons un axe, et cet axe correspond à 0° donc partons du principe que le soleil à l'aube n'est pas levé. Donc il doit être à -15, -20 degrés en dessous de la ligne d'horizon. Ça veut dire la ligne qui partage la moitié de l'écran. Ensuite nous avons le clic bouton `Lever du soleil`. Celui-ci doit générer le lever du soleil à environ 15, 20 degrés au-dessus de l'horizon, donc au-dessus de la ligne médiane de la moitié de l'écran. Ensuite vous avez le matin, le matin, le soleil, vous le montez un peu plus haut. Donc là, vous le montez à peu près à 35, 40 degrés. Ok. Ensuite vous avez midi. À midi, vous le mettez tout en haut. Donc là, vous le mettez le plus haut possible, à 90 degrés, quasiment en haut. Et ensuite, le bouton après-midi, quand on clique dessus, le bouton doit rejoindre, pour être cohérent, on va dire, les 60 degrés. Parce que l'après-midi, c'est l'après-midi, il n'est pas bas sur l'horizon le soleil, on est d'accord. Le coucher de soleil par contre, là, vous allez le mettre beaucoup plus bas, forcément. Donc ça rejoint le coucher du soleil, rejoint quasiment la même position que le lever du soleil. Comme ça, il n'y a pas d'ambiguïté, au moins c'est bien, voilà, parce que quand on clique sur un bouton et puis sur un autre, comme ça au moins c'est bien cohérent. Et ensuite le crépuscule, et bien là vous le faites passer en dessous de l'horizon, donc à moins 20°C ou moins 25°C. 

















