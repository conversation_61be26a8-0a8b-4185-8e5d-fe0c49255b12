import React, { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  triggerMorning: () => void;
  triggerMidday: () => void;
  triggerAfternoon: () => void;
  triggerSunset: () => void;
  triggerDawn: () => void;
  triggerDusk: () => void;
  triggerNight: () => void;
  resetSun: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean;
}

// 🔧 CISCO: NOUVEAU SYSTÈME DE COORDONNÉES SOLAIRES
// Ligne d'horizon = 0° (milieu de l'écran)
// Valeurs négatives = sous l'horizon, positives = au-dessus
const SUN_POSITIONS = {
  dawn: { angle: -15, horizontalOffset: -60 },      // Sous horizon, position Est
  sunrise: { angle: 25, horizontalOffset: -30 },    // Au-dessus horizon, légèrement Est
  morning: { angle: 65, horizontalOffset: -20 },    // Bien plus haut, courbe vers gauche
  midday: { angle: 90, horizontalOffset: -10 },     // Zénith, légèrement à gauche
  afternoon: { angle: 60, horizontalOffset: 20 },   // Descente symétrique, vers droite
  sunset: { angle: 25, horizontalOffset: 45 },      // Même hauteur que lever, position Ouest
  dusk: { angle: -20, horizontalOffset: 60 },       // Sous horizon, position Ouest
  night: { angle: -25, horizontalOffset: 0 }        // Très bas, position centrale
} as const;

// 🔧 CISCO: Fonction de conversion angle → position CSS
const angleToPosition = (angle: number, horizontalOffset: number = 0) => {
  // Conversion angle en position Y (0° = milieu écran)
  // -90° = 100% (tout en bas), +90° = -100% (tout en haut)
  const yPosition = -angle * (100 / 90); // Conversion linéaire
  
  // Position X basée sur l'offset horizontal
  const xPosition = horizontalOffset;
  
  return { y: `${yPosition}%`, x: `${xPosition}%` };
};

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunGlowRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🔧 CISCO: FONCTION UTILITAIRE - Animer le soleil vers une position
    const animateSunToPosition = (
      targetPosition: keyof typeof SUN_POSITIONS,
      duration: number = 15.0,
      glowIntensity: number = 1.0,
      flareIntensity: number = 0.7
    ) => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn(`☀️ Éléments DOM non prêts pour l'animation ${targetPosition}`);
        return;
      }

      console.log(`☀️ Animation vers ${targetPosition} - Angle: ${SUN_POSITIONS[targetPosition].angle}°, Offset: ${SUN_POSITIONS[targetPosition].horizontalOffset}%`);

      // Nettoyer l'animation précédente
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log(`✨ Animation ${targetPosition} terminée - Position finale: ${SUN_POSITIONS[targetPosition].angle}°`);
        }
      });

      // Calculer la position cible
      const targetPos = angleToPosition(
        SUN_POSITIONS[targetPosition].angle,
        SUN_POSITIONS[targetPosition].horizontalOffset
      );

      console.log(`🎯 Position cible calculée: y=${targetPos.y}, x=${targetPos.x}`);

      // PHASE 1: Mouvement du soleil avec courbe parabolique
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: targetPos.y,
          x: targetPos.x,
          duration: duration,
          ease: 'power2.inOut' // Courbe naturelle
        },
        0
      );

      // PHASE 2: Animation de la lueur synchronisée
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: glowIntensity,
          scale: 0.8 + (glowIntensity * 0.6), // Scale basé sur l'intensité
          duration: duration * 0.8,
          ease: 'power2.out'
        },
        duration * 0.1 // Démarre après 10% de la durée
      );

      // PHASE 3: Animation du lens flare
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: flareIntensity,
          duration: duration * 0.6,
          ease: 'power2.out'
        },
        duration * 0.2 // Démarre après 20% de la durée
      );
    };

    // 🌅 CISCO: Animation AUBE - Soleil sous l'horizon (-15°)
    const triggerDawn = () => {
      animateSunToPosition('dawn', 15.0, 0, 0);
    };

    // 🌅 CISCO: Animation LEVER DE SOLEIL - 25° au-dessus horizon
    const triggerSunrise = () => {
      animateSunToPosition('sunrise', 15.0, 1.0, 0.7);
    };

    // 🌄 CISCO: Animation MATIN - 65° bien plus haut
    const triggerMorning = () => {
      animateSunToPosition('morning', 20.0, 1.1, 0.8);
    };

    // ☀️ CISCO: Animation MIDI/ZÉNITH - 90° tout en haut
    const triggerMidday = () => {
      animateSunToPosition('midday', 15.0, 1.3, 1.0);
    };

    // 🌇 CISCO: Animation APRÈS-MIDI - 60° descente symétrique
    const triggerAfternoon = () => {
      animateSunToPosition('afternoon', 15.0, 1.2, 0.8);
    };

    // 🌅 CISCO: Animation COUCHER DE SOLEIL - 25° même hauteur que lever
    const triggerSunset = () => {
      animateSunToPosition('sunset', 15.0, 1.3, 0.7);
    };

    // 🌆 CISCO: Animation CRÉPUSCULE - Soleil sous l'horizon (-20°)
    const triggerDusk = () => {
      animateSunToPosition('dusk', 15.0, 0, 0);
    };

    // 🌌 CISCO: Animation NUIT PROFONDE - Soleil très bas (-25°)
    const triggerNight = () => {
      animateSunToPosition('night', 15.0, 0, 0);
    };

    // 🔄 CISCO: Remettre le soleil en position initiale
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      if (sunWrapperRef.current && sunGlowRef.current && lensFlareRef.current) {
        const initialPos = angleToPosition(SUN_POSITIONS.dawn.angle, SUN_POSITIONS.dawn.horizontalOffset);
        
        gsap.set(sunWrapperRef.current, {
          y: initialPos.y,
          x: initialPos.x,
          opacity: 1
        });
        gsap.set(sunGlowRef.current, {
          opacity: 0,
          scale: 0.8
        });
        gsap.set(lensFlareRef.current, {
          opacity: 0,
          rotation: 0
        });
      }

      console.log('🔄 Soleil remis en position initiale (aube)');
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      triggerMorning,
      triggerMidday,
      triggerAfternoon,
      triggerSunset,
      triggerDawn,
      triggerDusk,
      triggerNight,
      resetSun
    }));

    // Cleanup à la destruction du composant
    useLayoutEffect(() => {
      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 1.8 }}
      >
        {/* Conteneur pour le soleil et ses effets */}
        <div
          ref={sunWrapperRef}
          className="absolute w-52 h-52 left-1/2 top-1/2 -translate-x-1/2"
          style={{
            transform: 'translateX(-50%) translateY(16.67%)', // Position initiale aube (-15°)
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Lueur subtile du soleil */}
            <div
              ref={sunGlowRef}
              className="sun-glow absolute inset-0 opacity-0"
            />
            
            {/* EFFET 2: Soleil principal */}
            <img
              ref={sunImageRef}
              src="/SUN.png"
              alt="Soleil"
              className="w-full h-full object-contain"
              style={{
                filter: 'brightness(1.1) saturate(1.1)',
                zIndex: 10
              }}
            />

            {/* EFFET 3: Lens Flare */}
            <div
              ref={lensFlareRef}
              className="absolute opacity-0 pointer-events-none"
              style={{
                width: '600px',
                height: 'auto',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                transformOrigin: 'center center',
                zIndex: 15
              }}
            >
              <img
                src="/lens-flare-light-3508x2540.png"
                alt="Lens Flare"
                className="w-full h-auto"
                style={{
                  mixBlendMode: 'screen'
                }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
